import {Injectable, NotFoundException} from '@nestjs/common';
import {ContentCategory} from "@/entity/ContentCategory";
import {Content} from "@/entity/Content";
import slugify from "slugify";
import {ILike, Like} from "typeorm";
import {AudioTag} from "@/entity/AudioTag";
import {File} from "@/entity/File";
import * as cheerio from 'cheerio';
import {FileService} from "@/api/file/file.service";
import {copyFileSync, existsSync, rmSync} from "fs";
import {basename} from 'path'
import { AudioFile } from '@/entity/AudioFile';
import { Audio } from '@/entity/Audio';

@Injectable()
export class ContentService {
    constructor(private readonly fileService: FileService) {}

    async getAll() {
        return await Content.find({
            relations: ['category', 'tags', 'preview']
        });
    }

    async getBySlug(slug: string) {
        return await Content.find({
            where: {slug},
            relations: ['category', 'tags', 'preview', 'audioFiles', 'audio']
        });
    }
    async getTree() {
        let content = await ContentCategory.find({
            relations: ['contents', 'preview', 'contents.preview', 'contents.likes'],
            order: {
                order: 'ASC'
            }
        })
        return content.map(e => {
            return {
                ...e,
                contents: e.contents.filter(k => k.lang == 'ru')
            }
        })
    }
    async getCategories() {
        return await ContentCategory.find()
    }
    async getCategory(id: number) {
        if(!id) throw new NotFoundException('id not found');
        return await ContentCategory.findOne({
            where: {id},
            relations: ['preview']
        })
    }
    async create(dto: any, slug: string | null = null) {
        const ruTitle = dto.find(e => e.lang == 'ru')?.form?.title;
        const ruSlug = slug || slugify(ruTitle, {lower: true});

        for(let item of dto) {
            if(!item.form.title) continue

            if(item.form.preview) {
                item.form.preview = await this.fileService.save(item.form.preview.originalName, 'content', item.form.preview.name)
            }

            if(item.form.content) {
                const $ = cheerio.load(item.form.content);
                $('img').each(function() {
                    if($(this).attr('src').indexOf('upload/tmp')) {
                        const uploadIndex = $(this).attr('src').indexOf('/upload');
                        const result = '.' + $(this).attr('src').substring(uploadIndex);
                        const baseUrl = process.env.BUILD_ENV === 'local' ? 'http://localhost:9015' : 'https://dev.advayta.org'
                        if(existsSync(result)) {
                            copyFileSync(result, `./upload/content/${basename($(this).attr('src'))}`);
                            $(this).attr('src', `${baseUrl}/upload/content/${basename($(this).attr('src'))}`)
                        }
                    }
                });
                item.form.content = item.content = $('body').html();
            }

            let newTags = []
            const tags = item.form.tags ? item.form.tags : []
            for(let tag of tags) {
                const dbTag = await AudioTag.findOneBy({id: tag})
                newTags.push(dbTag)
            }

          const audio = item.form.audiofiles.filter(e => e.audio.type == 'audio');
          const audioFiles = item.form.audiofiles.filter(e => e.audio.type == 'audioFile');

          for(let audioItem of audio) {
            await Audio.update(audioItem.audio.id, {paid: audioItem.paid || false})
          }

          for(let audioFilesItem of audioFiles) {
            await AudioFile.update(audioFilesItem.audio.id, {paid: audioFilesItem.paid || false})
          }

            await Content.save({
                lang: item.lang,
                slug: ruSlug,
                title: item.form.title,
                seo_title: item.form.seo_title,
                seo_description: item.form.seo_description,
                content: item.form.content,
                category: item.form.category,
                telegram: item.form.telegram,
                instagram: item.form.instagram,
                email: item.form.email,
                phone: item.form.phone,
                active: item.form.active,
                preview: item.form.preview || null,
                buttons: item.form.buttons,
                author: item.form.author,
                youtube_links: item.form.youtube_links,
                tags: newTags,
                audioFiles: audioFiles.map(e => ({...e.audio, paid: e.paid})),
                audio: audio.map(e => ({...e.audio, paid: e.paid}))
            })
        }
        return { slug: ruSlug };
    }

    async update(slug: string, dto: any) {
        const content = await Content.find({
            where: {slug},
            relations: ['preview', 'audioFiles', 'audio']
        });

        for(let item of content) {
            const values = dto.find(e => e.lang == item.lang);
            if(!values) continue;
            const tags = values.form.tags ? values.form.tags : [];

            // Собираем старые изображения из текущего контента
            const oldImages = new Set<string>();
            if(item.content) {
                const $ = cheerio.load(item.content);
                $('img').each(function() {
                    const src = $(this).attr('src');
                    if(src.includes('/upload/')) {
                        oldImages.add(src);
                    }
                });
            }

            // Обновляем поля
            item.title = values.form.title;
            item.seo_title = values.form.seo_title;
            item.seo_description = values.form.seo_description;
            item.content = values.form.content;
            item.category = values.form.category;
            item.telegram = values.form.telegram;
            item.instagram = values.form.instagram;
            item.email = values.form.email;
            item.phone = values.form.phone;
            item.slug = values.form.slug;
            item.active = values.form.active;
            item.buttons = values.form.buttons;
            item.author = values.form.author;
            item.youtube_links = values.form.youtube_links;

            const audio = values.form.audiofiles.filter(e => e.audio.type == 'audio');
            const audioFiles = values.form.audiofiles.filter(e => e.audio.type == 'audioFile');

            item.audioFiles = audioFiles.map(e => ({...e.audio, paid: e.paid}));
            item.audio = audio.map(e => ({...e.audio, paid: e.paid}));

            for(let audioItem of audio) {
              await Audio.update(audioItem.audio.id, {paid: audioItem.paid || false})
            }

            for(let audioFilesItem of audioFiles) {
              await AudioFile.update(audioFilesItem.audio.id, {paid: audioFilesItem.paid || false})
            }

            if(values.form.preview && !values.form.preview?.id) {
                item.preview = await this.fileService.save(values.form.preview.originalName, 'content', values.form.preview.name, item.preview);
            }

            const newImages = new Set<string>();
            if(values.form.content) {
                const $ = cheerio.load(values.form.content);
                $('img').each(function() {
                    const src = $(this).attr('src');

                    // Обработка новых/перемещенных изображений
                    if(src.includes('upload/tmp')) {
                        const uploadIndex = src.indexOf('/upload');
                        const result = '.' + src.substring(uploadIndex);
                        const baseUrl = process.env.BUILD_ENV === 'local' ? 'http://localhost:9015' : 'https://dev.advayta.org';
                        if(existsSync(result)) {
                            copyFileSync(result, `./upload/content/${basename(src)}`);
                            const newSrc = `${baseUrl}/upload/content/${basename(src)}`;
                            $(this).attr('src', newSrc);
                            newImages.add(newSrc);
                        }
                    } else if(src.includes('/upload/content/')) {
                        newImages.add(src);
                    }
                });
                values.form.content = item.content = $('body').html();
            }

            // Удаляем изображения, которые были в старом контенте, но нет в новом
            oldImages.forEach(oldSrc => {
                if(!newImages.has(oldSrc)) {
                    const uploadIndex = oldSrc.indexOf('/upload');
                    const filePath = '.' + oldSrc.substring(uploadIndex);
                    if(existsSync(filePath)) {
                        rmSync(filePath, {recursive: true});
                    }
                }
            });

            let newTags = [];
            for(let tag of tags) {
                const dbTag = await AudioTag.findOneBy({id: tag});
                newTags.push(dbTag);
            }
            item.tags = newTags;

            const result = await item.save();
            dto.splice(dto.findIndex(e => e.lang == result.lang), 1);
        }

        const newLangItem = dto.filter(e => e.form.title);
        if(!newLangItem.length) return;
        await this.create(newLangItem, slug);
    }

    async delete(slug: string, replace: any = '') {
        const content = await Content.findOne({
            where: {slug},
            relations: ['preview']
        })
        let replaced = {
            id: null,
            link: null,
            title: null,
        }

        if(content.content) {
            const $ = cheerio.load(content.content);
            $('img').each(function() {
                const src = $(this).attr('src');
                const uploadIndex = $(this).attr('src').indexOf('/upload');
                const result = '.' + $(this).attr('src').substring(uploadIndex);
                if(existsSync(result)) {
                    rmSync(result, {recursive: true})
                }
            });
        }

        if(replace && replace != 'null') {
            const replacedContent = await Content.findOne({
                where: {id: replace},
                relations: ['category'],
            })
            if(replacedContent) {
                replaced.link = `/${replacedContent.lang}/categories/${replacedContent.category.id}/${replacedContent.slug}`;
                
                replaced.title = replacedContent.title
                replaced.id = replacedContent.id
            }
        }

        const contentList = await Content.findBy({
            content: ILike(`%data-content-id="${content.id}"%`),
        })

        for(let item of contentList) {
            const $ = cheerio.load(item.content);
            const mention = $('.mention[data-content-id="'+content.id+'"]')
            if(mention.length) {
                mention.each(function() {
                    if(replaced.id && replaced.title && replaced.link) {
                        $(this).attr('href', replaced.link)
                        $(this).attr('data-content-id', replaced.id)
                        $(this).attr('data-mention', `@${replaced.title}`)
                        $(this).text(replaced.title)
                    } else {
                        $(this).replaceWith(`<span>${$(this).text()}</span>`)
                    }
                })
                Content.update(item.id, {content: $('body').html()})
            }
        }

        if(content.preview && existsSync(`./upload/${content.preview.name}`)) {
            rmSync(`./upload/${content.preview.name}`)
        }

        return await Content.delete({slug})
    }
    async search(query: string) {
        return await Content.find({
            where: {
                title: ILike(`%${query}%`)
            },
            relations: ['category']
        })
    }
    async createCategory(dto: any) {
        if(dto.preview && !dto.preview?.id) {
            dto.preview = await this.fileService.save(dto.preview.originalName, 'content', dto.preview.name)
        }
        if(dto.id) {
            const category = await this.getCategory(dto.id);
            if(!dto.preview?.id) {
                dto.preview = await this.fileService.save(dto.preview.originalName, 'content', dto.preview.name, category.preview)
            }
            return await ContentCategory.update(dto.id, dto)
        }
        return await ContentCategory.save({
            title: dto.title,
            active: dto.active,
            preview: dto.preview,
            slug: slugify(dto.title, {lower: true})
        })
    }
    async deleteCategory(categoryId: number) {
        const content = await Content.findBy({
            category: {
                id: categoryId
            }
        })

        const category = await this.getCategory(categoryId);
        if(category.preview && existsSync(`./upload/${category.preview.name}`)) {
            rmSync(`./upload/${category.preview.name}`)
        }

        for(let item of content) await Content.delete(item.id)
        return await ContentCategory.delete(categoryId)
    }

    async getLinkedContent(slug: string) {
        const content = await Content.findOneBy({slug})

        const contentList = await Content.findBy({
            content: ILike(`%data-content-id="${content.id}"%`),
        })

        return contentList.length
    }

    async updateCategoryOrder(body) {
        return await Promise.all(body.categories.map(async item => {
            const category = await this.getCategory(item.id);
            category.order = item.order;
            return await category.save();
        }))
    }
}
